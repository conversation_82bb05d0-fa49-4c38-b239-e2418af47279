import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:fitsomnia_app/firebase_options.dart';
import 'package:fitsomnia_app/src/core/logger.dart';
import 'package:fitsomnia_app/src/core/routes/global_navigation.dart';
import 'package:fitsomnia_app/src/core/services/local_notification/local_notification_service.dart';
import 'package:fitsomnia_app/src/core/services/local_storage/cache_service.dart';
import 'package:flutter/foundation.dart';

class FirebaseService {
  static final FirebaseService _firebaseService = FirebaseService._internal();
  late FirebaseMessaging _firebaseMessaging;
  late NotificationSettings settings;
  late FirebaseAnalytics _analytics;

  FirebaseService._internal();

  factory FirebaseService() {
    return _firebaseService;
  }

  Future<void> initialize() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    _analytics = FirebaseAnalytics.instance;
    _analytics.setDefaultEventParameters(null);

    FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(!kDebugMode);

    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);

      return true;
    };

    _firebaseMessaging = FirebaseMessaging.instance;
    _setup();
  }

  Future<void> _setup() async {
    await _setupTokenRefreshListeners();
    await _iOSSetup();
    // await Future.delayed(const Duration(seconds: 5)); // APNS token needed time to reset. wait for token setup
    await retrieveFCMToken();

    // ✅ Subscribe to environment-specific topic
    await setupFcmTopic("fitsomnia-reminders");
  }

  Future<void> setupInteractedMessage() async {
    /// Initiate Local Notification Service
    LocalNotificationService localNotificationService =
        LocalNotificationService();
    await localNotificationService.setup();

    /// Update notification counter
    localNotificationService.getActiveNotification();

    /// Get any messages which caused the application to open from
    /// a terminated state.
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    /// handle navigation logic
    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }

    /// Also handle any interaction when the app is in the background via a
    /// Stream listener
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);

    /// Also handle any interaction when the app is in the foreground via a
    /// Stream listener
    FirebaseMessaging.onMessage.listen(_showNotification);
  }

  Future<void> _iOSSetup() async {
    if (Platform.isIOS) {
      bool notificationPermission =
          await _initiatePermissionStatusCheckForIOS();
      if (notificationPermission) {
        _enableForegroundNotificationForIOS();
      }
    }

    if (Platform.isAndroid) {
      _enableForegroundNotificationForIOS();
    }
  }

  Future<bool> _initiatePermissionStatusCheckForIOS() async {
    settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    Log.info('Notification Permission status: ${settings.authorizationStatus}');
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      Log.info('Notification Permission status: Authorized');

      return true;
    }

    return false;
  }

  /// Sets the presentation options for Apple notifications when received in
  /// the foreground.
  ///
  /// By default, on Apple devices notification messages are only shown when
  /// the application is in the background or terminated. Calling this method
  /// updates these options to allow customizing notification presentation behavior whilst
  /// the application is in the foreground.
  void _enableForegroundNotificationForIOS() {
    _firebaseMessaging.setForegroundNotificationPresentationOptions(
      badge: true,
      alert: true,
      sound: true,
    );
  }

  Future<void> retrieveFCMToken() async {
    try {
      bool isMessagingAllowed = await _firebaseMessaging.isSupported();
      Log.debug('FCM messaging supported: $isMessagingAllowed');

      if (Platform.isIOS) {
        String? apnsToken = await _getAPNSTokenWithRetry();
        Log.debug('iOS APNS token: $apnsToken');

        if (apnsToken != null) {
          String? token = await _firebaseMessaging.getToken();
          Log.debug('iOS FCM token: $token');
          if (token != null) await CacheService.instance.storeFcmToken(token);
        }
      } else {
        String? token = await _firebaseMessaging.getToken();
        Log.debug('Android FCM token: $token');
        if (token != null) await CacheService.instance.storeFcmToken(token);
      }
    } catch (e) {
      Log.error('Error retrieving FCM token: $e');
    }
  }

  Future<String?> _getAPNSTokenAsync() async {
    String? apnsToken;

    try {
      await Future.delayed(const Duration(seconds: 2));
      apnsToken = await _firebaseMessaging.getAPNSToken();
      if (apnsToken != null) {
        return apnsToken;
      }
    } catch (e) {
      Log.error('Error retrieving APNS token: $e');
    }

    if (apnsToken == null) {
      try {
        await Future.delayed(const Duration(seconds: 2));
        apnsToken = await _firebaseMessaging.getAPNSToken();
      } catch (e) {
        Log.error('Error retrieving APNS token(2): $e');
      }
    }

    return apnsToken;
  }

  Future<String?> _getAPNSTokenWithRetry({
    int maxAttempts = 5,
    int delaySeconds = 2,
  }) async {
    String? apnsToken;
    int attempts = 0;

    while (attempts < maxAttempts && apnsToken == null) {
      try {
        await Future.delayed(Duration(seconds: delaySeconds));
        apnsToken = await _firebaseMessaging.getAPNSToken();

        if (apnsToken != null) {
          Log.debug('APNS token retrieved after ${attempts + 1} attempts');

          return apnsToken;
        }
      } catch (e) {
        Log.error('Error retrieving APNS token (attempt ${attempts + 1}): $e');
      }
      attempts++;
    }

    return null;
  }

  Future<String?> _getAPNSTokenAsyncLoop() async {
    String? apnsToken;

    while (apnsToken == null) {
      await Future.delayed(const Duration(seconds: 1));
      apnsToken = await _firebaseMessaging.getAPNSToken();
      Log.debug('Retrive apnsToken: $apnsToken');
    }

    return apnsToken;
  }

  Future<String?> _getFCMTokenAsync() async {
    await Future.delayed(const Duration(seconds: 2));
    String? fcmToken = await _firebaseMessaging.getToken();

    return fcmToken;
  }

  void logGAEvents(String name, Map<String, Object> parameters) async {
    Log.debug('google_analytics: $name');
    await _analytics.logEvent(name: name, parameters: parameters);
  }

  void logFeatureUsage(
    String featureName,
    String pageName,
    String pageIdx,
  ) async {
    Log.debug('google-analytics: feature_usage: $featureName $pageName');
    await _analytics.logEvent(
      name: 'feature_usage',
      parameters: {
        'feature_name': featureName,
        'page_name': pageName,
        'page_idx': pageIdx,
      },
    );
  }

  /// Show notification when App is in the foreground
  Future<void> _showNotification(
    RemoteMessage remoteMessage,
  ) async {
    await LocalNotificationService().showNotification(
      remoteMessage: remoteMessage,
    );
  }

  /// Refresh FCM Token
  Future<void> _setupTokenRefreshListeners() async {
    _firebaseMessaging.onTokenRefresh.listen(
      (token) async {
        Log.info('On Refresh FCM token: $token');
        await CacheService.instance.storeFcmToken(token);
      },
    );
  }

  /// Handle Navigation when tapped on the Push Notification while App is closed
  /// or in the background
  Future<void> _handleMessage(RemoteMessage message) async {
    Map<String, dynamic> data = message.data;

    // Log notification analytics using improved dynamic mapping
    _logNotificationOpenedEvent(data);

    /// Top-Level handle notification method
    handleMessage(data);
  }

  /// Improved dynamic notification analytics logger
  void _logNotificationOpenedEvent(Map<String, dynamic> data) {
    final notificationId = (data['notificationId'] ?? '').toString();
    final type = (data['type'] ?? '').toString().trim().toLowerCase();
    final topic = (data['topic'] ?? '').toString().trim().toLowerCase();
    final title = data['title'] ?? '';
    final body = data['body'] ?? '';

    // Decide pageName dynamically based on topic/type
    String pageName;
    if (topic.isNotEmpty) {
      pageName = '$topic-notification';
    } else if (type.isNotEmpty) {
      pageName = '$type-notification';
    } else {
      pageName = 'general-notification';
    }

    // Log using the reusable method
    logFeatureUsage(
      'Notification', // feature_name
      pageName, // page_name
      notificationId, // page_idx
    );

    // Debug logging for QA purposes
    Log.debug(
        'Notification opened: id=$notificationId, type=$type, topic=$topic, title=$title, body=$body');
  }

  Future<void> setupFcmTopic(String baseTopic) async {
    // baseTopic example: "fitsomnia-reminders"

    // Build full topic (same format as backend)
    const String environment =
        String.fromEnvironment('ENV', defaultValue: 'LOCAL');
    final String environmentTopic = "$baseTopic-${environment.toLowerCase()}";

    // Unsubscribe from all to avoid duplicates
    await FirebaseMessaging.instance
        .unsubscribeFromTopic("fitsomnia-reminders-local");
    await FirebaseMessaging.instance
        .unsubscribeFromTopic("fitsomnia-reminders-staging");
    await FirebaseMessaging.instance
        .unsubscribeFromTopic("fitsomnia-reminders-server");

    // Subscribe only to the correct one
    await FirebaseMessaging.instance.subscribeToTopic(environmentTopic);

    print("✅ Subscribed to $environmentTopic");
  }
}
